<template>
	<view class="test-container">
		<view class="test-header">
			<text class="test-title">赠送规则页面测试</text>
		</view>
		
		<view class="test-content">
			<view class="test-section">
				<text class="section-title">API接口测试</text>
				
				<button class="test-btn" @click="testGetGiftRuleList">测试获取规则列表</button>
				<button class="test-btn" @click="testAddGiftRule">测试添加规则</button>
				<button class="test-btn" @click="testGetCourseList">测试获取课程列表</button>
			</view>
			
			<view class="test-section">
				<text class="section-title">测试结果</text>
				<view class="test-result">
					<text class="result-text">{{ testResult }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { 
	getGiftRuleList, 
	addGiftRule, 
	getCourseList 
} from '@/api/api.js'
import { toast } from '@/utils/utils.js'

export default {
	data() {
		return {
			testResult: '等待测试...'
		}
	},
	
	methods: {
		async testGetGiftRuleList() {
			try {
				this.testResult = '正在测试获取规则列表...'
				const response = await getGiftRuleList({
					pageNum: 1,
					pageSize: 10
				})
				
				this.testResult = `获取规则列表成功：\n${JSON.stringify(response, null, 2)}`
				toast('测试成功')
			} catch (error) {
				this.testResult = `获取规则列表失败：\n${error.message}`
				toast('测试失败')
			}
		},
		
		async testAddGiftRule() {
			try {
				this.testResult = '正在测试添加规则...'
				const testData = {
					ruleName: '测试规则',
					ruleType: 'amount_based',
					description: '这是一个测试规则',
					triggerAmount: 100,
					giftCourseId: 1,
					giftCourseName: '测试课程',
					giftSessions: 5,
					status: '1'
				}
				
				const response = await addGiftRule(testData)
				this.testResult = `添加规则成功：\n${JSON.stringify(response, null, 2)}`
				toast('测试成功')
			} catch (error) {
				this.testResult = `添加规则失败：\n${error.message}`
				toast('测试失败')
			}
		},
		
		async testGetCourseList() {
			try {
				this.testResult = '正在测试获取课程列表...'
				const response = await getCourseList({
					pageNum: 1,
					pageSize: 10
				})
				
				this.testResult = `获取课程列表成功：\n${JSON.stringify(response, null, 2)}`
				toast('测试成功')
			} catch (error) {
				this.testResult = `获取课程列表失败：\n${error.message}`
				toast('测试失败')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.test-container {
	padding: 40rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.test-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.test-content {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 40rpx;
}

.test-section {
	margin-bottom: 40rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.test-btn {
	display: block;
	width: 100%;
	padding: 24rpx;
	margin-bottom: 16rpx;
	background: #9C27B0;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 500;
	
	&:last-child {
		margin-bottom: 0;
	}
	
	&:active {
		background: #7B1FA2;
	}
}

.test-result {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	min-height: 200rpx;
}

.result-text {
	font-size: 24rpx;
	color: #333;
	line-height: 1.5;
	white-space: pre-wrap;
	word-break: break-all;
}
</style>
