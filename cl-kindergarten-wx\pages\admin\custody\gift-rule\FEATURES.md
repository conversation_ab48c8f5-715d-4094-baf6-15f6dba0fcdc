# 赠送规则页面功能说明

## 功能概览

### 1. 禁用按钮功能 ✅

**功能描述**: 支持规则的启用和禁用操作

**操作流程**:
1. 在规则卡片中点击"启用"或"禁用"按钮
2. 系统弹出确认对话框
3. 确认后调用后端API更新规则状态
4. 刷新列表显示最新状态

**按钮状态**:
- 启用状态：显示"禁用"按钮（红色）
- 禁用状态：显示"启用"按钮（蓝色）

**API接口**: `PUT /business/gift/rule/changeStatus`

### 2. 赠送课程下拉框 ✅

**功能描述**: 提供全部课程的下拉选择功能

**主要特性**:
- 📋 显示全部可用课程
- 🔍 支持课程名称搜索
- 📝 显示课程描述信息
- ✅ 选中状态视觉反馈
- 📱 响应式设计

**使用方式**:
1. 点击课程选择框展开下拉列表
2. 可在搜索框中输入关键词过滤课程
3. 点击课程项目进行选择
4. 选中的课程会显示勾选图标

## 技术实现

### 禁用按钮实现

```vue
<!-- 模板部分 -->
<button 
  class="action-btn" 
  :class="item.status === '1' ? 'danger' : 'primary'" 
  @click.stop="handleStatusChange(item)">
  <text class="btn-text">{{ item.status === '1' ? '禁用' : '启用' }}</text>
</button>

<!-- 方法实现 -->
async handleStatusChange(item) {
  const action = item.status === '1' ? '禁用' : '启用'
  
  uni.showModal({
    title: '确认操作',
    content: `确定要${action}规则"${item.ruleName}"吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const newStatus = item.status === '1' ? '0' : '1'
          const response = await changeGiftRuleStatus({
            ruleId: item.ruleId,
            status: newStatus
          })
          
          if (response.code === 200) {
            toast(`${action}成功`)
            this.loadGiftRuleList(true)
          }
        } catch (error) {
          toast(`${action}失败，请重试`)
        }
      }
    }
  })
}
```

### 课程下拉框实现

```vue
<!-- 模板部分 -->
<view class="course-select-container">
  <view class="course-select-header" @click="toggleCourseDropdown">
    <text class="course-select-text">{{ formData.giftCourseName || '请选择赠送课程' }}</text>
    <u-icon name="arrow-down" :class="{ 'rotate': showCourseDropdown }"></u-icon>
  </view>
  
  <view v-if="showCourseDropdown" class="course-dropdown">
    <view class="course-search">
      <input 
        class="course-search-input"
        placeholder="搜索课程名称"
        v-model="courseSearchKeyword"
        @input="filterCourses"
      />
    </view>
    
    <scroll-view class="course-list" scroll-y="true">
      <view 
        v-for="course in filteredCourseOptions" 
        :key="course.courseId"
        class="course-item"
        :class="{ active: formData.giftCourseId === course.courseId }"
        @click="selectCourse(course)">
        <view class="course-info">
          <text class="course-name">{{ course.courseName }}</text>
          <text class="course-desc">{{ course.description || '暂无描述' }}</text>
        </view>
        <view v-if="formData.giftCourseId === course.courseId" class="course-selected">
          <u-icon name="checkmark" size="20" color="#9C27B0"></u-icon>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 方法实现 -->
toggleCourseDropdown() {
  this.showCourseDropdown = !this.showCourseDropdown
  if (this.showCourseDropdown) {
    this.courseSearchKeyword = ''
    this.filteredCourseOptions = [...this.courseOptions]
  }
},

selectCourse(course) {
  this.formData.giftCourseId = course.courseId
  this.formData.giftCourseName = course.courseName
  this.showCourseDropdown = false
  this.courseSearchKeyword = ''
},

filterCourses() {
  if (!this.courseSearchKeyword.trim()) {
    this.filteredCourseOptions = [...this.courseOptions]
  } else {
    const keyword = this.courseSearchKeyword.toLowerCase()
    this.filteredCourseOptions = this.courseOptions.filter(course => 
      course.courseName.toLowerCase().includes(keyword) ||
      (course.description && course.description.toLowerCase().includes(keyword))
    )
  }
}
```

## 数据结构

### 课程对象
```javascript
{
  courseId: Number,        // 课程ID
  courseName: String,      // 课程名称
  description: String,     // 课程描述
  // 其他课程属性...
}
```

### 状态管理
```javascript
data() {
  return {
    showCourseDropdown: false,      // 课程下拉框显示状态
    courseSearchKeyword: '',        // 课程搜索关键词
    courseOptions: [],              // 全部课程选项
    filteredCourseOptions: [],      // 过滤后的课程选项
  }
}
```

## 样式设计

### 课程下拉框样式特点
- **响应式设计**: 适配不同屏幕尺寸
- **搜索框**: 顶部固定搜索输入框
- **滚动列表**: 支持大量课程的滚动浏览
- **选中状态**: 清晰的视觉反馈
- **动画效果**: 平滑的展开收起动画

### 按钮样式
- **状态区分**: 不同状态使用不同颜色
- **交互反馈**: 点击时的视觉反馈
- **一致性**: 与整体设计风格保持一致

## 用户体验

### 操作便捷性
1. **一键切换**: 状态切换只需一次点击
2. **搜索过滤**: 快速找到目标课程
3. **确认机制**: 重要操作有确认提示
4. **即时反馈**: 操作结果立即显示

### 视觉体验
1. **状态清晰**: 规则状态一目了然
2. **选择直观**: 课程选择过程直观
3. **信息完整**: 显示课程的详细信息
4. **响应流畅**: 交互响应迅速

## 注意事项

1. **网络异常**: 处理网络请求失败的情况
2. **数据验证**: 确保选择的课程有效
3. **状态同步**: 操作后及时更新界面状态
4. **用户提示**: 提供清晰的操作反馈

## 扩展建议

1. **批量操作**: 支持批量启用/禁用规则
2. **课程分类**: 按课程类别分组显示
3. **最近选择**: 记录最近选择的课程
4. **快捷操作**: 添加快捷操作按钮
