import request from '@/utils/request'
//使用说明
//  export const getList = data => request.get('/api/list', data, false)
//              页面调用名  请求参数       请求类型  接口地址         loading是否显示
/*页面中调用方法：（若无请求参数则留空,例：this.$api.getList()）
this.$api.getList(params).then(res => {
	
})
*/
//列表
export const getList = data => request.get('/list', data)
//登陆
export const login = data => request.post('/login', data)
//注册
export const register = data => request.post('/register', data)
//忘记密码
export const forgetPassWord = data => request.post('/forgetPassWord', data)
//登陆用户信息
export const baseInfo = data => request.get('/getInfo', data)
//保存用户信息
export const baseInfoSave = data => request.post('/saveInfo', data)
//检测版本升级
export const checkVersion = data => request.post('/checkVersion2', data,false)
//文章详情
export const detail = data => request.get('/detail', data,false)
//获取单页内容
export const page = data => request.get('/page', data)
//提交实名认证资料
export const auth = data => request.post('/auth', data)
//修改密码
export const password = data => request.post('/password', data)
//注销帐号
export const logout_account = data => request.post('/logout_account', data)
//修改手机号
export const feedback = data => request.post('/feedback', data)
//微信授权手机号登录
export const wechatPhoneLogin = data => request.post('/wechatPhoneLogin', data)
//获取图形验证码
export const getCaptcha = () => request.get('/captchaImage')

// ========== 物品管理相关接口 ==========
// 查询物品列表
export const getItemList = data => request.get('/business/item/list', data)
// 查询物品详细
export const getItemDetail = itemId => request.get(`/business/item/${itemId}`)
// 新增物品
export const addItem = data => request.post('/business/item', data)
// 修改物品
export const updateItem = data => request.put('/business/item', data)
// 删除物品
export const deleteItem = itemId => request.delete(`/business/item/${itemId}`)

// ========== 物品类别相关接口 ==========
// 查询物品类别列表
export const getCategoryList = data => request.get('/business/category/list', data)
// 查询物品类别详细
export const getCategoryDetail = categoryId => request.get(`/business/category/${categoryId}`)
// 新增物品类别
export const addCategory = data => request.post('/business/category', data)
// 修改物品类别
export const updateCategory = data => request.put('/business/category', data)
// 删除物品类别
export const deleteCategory = categoryId => request.delete(`/business/category/${categoryId}`)

// ========== 教师管理相关接口 ==========
// 查询教师列表
export const getTeacherList = data => request.get('/business/teacher/list', data)
// 查询教师详细
export const getTeacherDetail = teacherId => request.get(`/business/teacher/${teacherId}`)
// 新增教师
export const addTeacher = data => request.post('/business/teacher', data)
// 修改教师
export const updateTeacher = data => request.put('/business/teacher', data)
// 删除教师
export const deleteTeacher = teacherId => request.delete(`/business/teacher/${teacherId}`)
// 同步教师数据
export const syncTeacherData = () => request.post('/business/dingtalk/syncUsers')

// ========== 学生管理相关接口 ==========
// 查询学生列表
export const getStudentList = data => request.get('/business/student/list', data)
// 查询学生详细
export const getStudentDetail = studentId => request.get(`/business/student/${studentId}`)
// 新增学生
export const addStudent = data => request.post('/business/student', data)
// 修改学生
export const updateStudent = data => request.put('/business/student', data)
// 删除学生
export const deleteStudent = studentId => request.delete(`/business/student/${studentId}`)
// 同步学生数据
export const syncStudentData = () => request.post('/business/student/sync')

// ========== 班级管理相关接口 ==========
// 查询班级列表
export const getClassList = data => request.get('/business/class/list', data)
// 查询班级详细
export const getClassDetail = classId => request.get(`/business/class/${classId}`)
// 新增班级
export const addClass = data => request.post('/business/class', data)
// 修改班级
export const updateClass = data => request.put('/business/class', data)
// 删除班级
export const deleteClass = classId => request.delete(`/business/class/${classId}`)
// 获取所有班级选项（用于下拉框）
export const getClassOptions = () => request.get('/business/class/options')
// 获取所有班级列表（用于选择）
export const getAllClassList = () => request.get('/business/class/listAll')
// 导出班级
export const exportClass = data => request.get('/business/class/export', data)
// 同步钉钉部门到班级
export const syncDingtalkDepartments = () => request.post('/business/class/dingtalk/syncDepartments')
// 同步班级当前人数
export const syncClassCurrentCount = () => request.post('/business/class/syncCurrentCount')
// 同步指定班级当前人数
export const syncClassCurrentCountById = classId => request.post(`/business/class/syncCurrentCount/${classId}`)

// ========== 赠送规则相关接口 ==========
// 查询赠送规则列表
export const getGiftRuleList = data => request.get('/business/gift/rule/list', data)
// 查询赠送规则详细
export const getGiftRuleDetail = ruleId => request.get(`/business/gift/rule/${ruleId}`)
// 新增赠送规则
export const addGiftRule = data => request.post('/business/gift/rule', data)
// 修改赠送规则
export const updateGiftRule = data => request.put('/business/gift/rule', data)
// 删除赠送规则
export const deleteGiftRule = ruleId => request.delete(`/business/gift/rule/${ruleId}`)
// 查询启用的赠送规则列表
export const getActiveGiftRules = () => request.get('/business/gift/rule/active')
// 根据规则类型查询启用的规则
export const getActiveGiftRulesByType = ruleType => request.get(`/business/gift/rule/active/${ruleType}`)
// 修改赠送规则状态
export const changeGiftRuleStatus = data => request.put('/business/gift/rule/changeStatus', data)

// ========== 赠送记录相关接口 ==========
// 查询赠送记录列表
export const getGiftRecordList = data => request.get('/business/gift/record/list', data)
// 查询赠送记录详细
export const getGiftRecordDetail = recordId => request.get(`/business/gift/record/${recordId}`)
// 新增赠送记录
export const addGiftRecord = data => request.post('/business/gift/record', data)
// 修改赠送记录
export const updateGiftRecord = data => request.put('/business/gift/record', data)
// 删除赠送记录
export const deleteGiftRecord = recordId => request.delete(`/business/gift/record/${recordId}`)
// 查询学生的有效赠送记录
export const getStudentGiftRecords = studentId => request.get(`/business/gift/record/student/${studentId}`)
// 更新赠送记录的使用课时
export const updateUsedSessions = (recordId, usedSessions) => request.put(`/business/gift/record/${recordId}/useSessions/${usedSessions}`)

// ==================== 课时统计相关接口 ====================
// 获取学生考勤统计
export const getStudentAttendanceStatistics = (data) => {
	return request({
		url: '/business/attendance-statistics/student',
		method: 'GET',
		params: data
	})
}

// 获取教师考勤统计
export const getTeacherAttendanceStatistics = (data) => {
	return request({
		url: '/business/attendance-statistics/teacher',
		method: 'GET',
		params: data
	})
}

// 获取班级考勤统计
export const getClassAttendanceStatistics = (data) => {
	return request({
		url: '/business/attendance-statistics/class',
		method: 'GET',
		params: data
	})
}

// 获取考勤汇总报表
export const getAttendanceSummary = (data) => {
	return request({
		url: '/business/attendance-statistics/summary',
		method: 'GET',
		params: data
	})
}

// 批量确认考勤记录
export const confirmAttendanceBatch = (attendanceIds) => {
	return request({
		url: '/business/attendance-statistics/confirm-batch',
		method: 'POST',
		params: { attendanceIds }
	})
}

// 生成月度考勤报表
export const generateMonthlyReport = (year, month) => {
	return request({
		url: '/business/attendance-statistics/monthly-report',
		method: 'POST',
		params: { year, month }
	})
}

// 考勤异常处理
export const handleAttendanceException = (attendanceId, exceptionType, reason) => {
	return request({
		url: '/business/attendance-statistics/handle-exception',
		method: 'POST',
		params: { attendanceId, exceptionType, reason }
	})
}

// ==================== 教师课时统计相关接口 ====================
// 查询教师课时统计列表
export const getTeacherCourseStatsList = (data) => request.get('/business/teacher-course-stats/list', data)

// 计算教师课时统计
export const calculateTeacherCourseStats = (data) => request.post('/business/teacher-course-stats/calculate', data)

// 重新计算教师课时统计
export const recalculateTeacherCourseStats = (data) => request.post('/business/teacher-course-stats/recalculate', data)

// 获取教师课时统计汇总
export const getTeacherCourseStatsSummary = (data) => request.get('/business/teacher-course-stats/summary', data)

// 获取教师详细授课记录
export const getTeacherCourseDetails = (teacherId, data) => request.get(`/business/teacher-course-stats/details/${teacherId}`, data)

// 导出教师课时统计
export const exportTeacherCourseStats = (data) => request.get('/business/teacher-course-stats/export', data)

// ==================== 教师管理相关接口 ====================
// 获取所有教师列表
export const listAllTeacher = (data) => request.get('/business/teacher/allList', data)

// ==================== 课程管理相关接口 ====================
// 获取所有课程列表
export const listAllCourse = (data) => request.get('/business/course/listAll', data)

// ========== 课程管理相关接口 ==========
// 查询课程列表
export const getCourseList = (data) => request.get('/business/course/list', data)

// 获取所有课程列表（用于选择）
export const getAllCourseList = () => request.get('/business/course/listAll')
