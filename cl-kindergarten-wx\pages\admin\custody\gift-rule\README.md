# 赠送规则管理页面

## 功能概述

微信小程序端的赠送规则管理页面，用于管理幼儿园的课时赠送规则。支持基于金额和出勤率的两种赠送规则类型。

## 主要功能

### 1. 规则列表展示
- 显示所有赠送规则的列表
- 支持分页加载
- 显示规则名称、类型、状态等基本信息
- 显示触发条件和赠送内容

### 2. 搜索和筛选
- 支持按规则名称搜索
- 支持按规则类型筛选（金额型/出勤型）
- 支持按状态筛选（启用/禁用）

### 3. 新增规则
- 支持创建新的赠送规则
- 表单验证确保数据完整性
- 支持两种规则类型：
  - 金额型：基于消费金额触发
  - 出勤型：基于出勤率触发

### 4. 编辑规则
- 支持修改现有规则的所有属性
- 保持数据一致性

### 5. 状态管理
- 支持启用/禁用规则
- 确认操作防止误操作

### 6. 删除规则
- 支持删除不需要的规则
- 确认操作防止误删除

## 数据结构

### 规则对象 (KgGiftRule)
```javascript
{
  ruleId: Number,           // 规则ID
  ruleName: String,         // 规则名称
  ruleType: String,         // 规则类型：amount_based/attendance_based
  description: String,      // 规则描述
  status: String,           // 状态：0-禁用，1-启用
  triggerAmount: Number,    // 触发金额（金额型规则）
  triggerAttendanceRate: Number, // 触发出勤率（出勤型规则）
  giftCourseId: Number,     // 赠送课程ID
  giftCourseName: String,   // 赠送课程名称
  giftSessions: Number,     // 赠送课时数
  effectiveDate: String,    // 生效日期
  expiryDate: String,       // 失效日期
  maxTimesPerStudent: Number, // 每人最大享受次数
  maxTimesPerMonth: Number, // 每人每月最大次数
}
```

## API接口

### 1. 获取规则列表
- **接口**: `GET /business/gift/rule/list`
- **参数**: pageNum, pageSize, ruleName, ruleType, status
- **返回**: 分页的规则列表

### 2. 获取规则详情
- **接口**: `GET /business/gift/rule/{ruleId}`
- **参数**: ruleId
- **返回**: 规则详细信息

### 3. 新增规则
- **接口**: `POST /business/gift/rule`
- **参数**: 规则对象
- **返回**: 操作结果

### 4. 修改规则
- **接口**: `PUT /business/gift/rule`
- **参数**: 规则对象
- **返回**: 操作结果

### 5. 删除规则
- **接口**: `DELETE /business/gift/rule/{ruleId}`
- **参数**: ruleId
- **返回**: 操作结果

### 6. 修改规则状态
- **接口**: `PUT /business/gift/rule/changeStatus`
- **参数**: { ruleId, status }
- **返回**: 操作结果

### 7. 获取课程列表
- **接口**: `GET /business/course/list`
- **参数**: pageNum, pageSize
- **返回**: 课程列表（用于选择赠送课程）

## 使用说明

### 1. 查看规则列表
- 进入页面后自动加载规则列表
- 可以通过搜索框搜索特定规则
- 可以通过筛选按钮筛选不同类型和状态的规则

### 2. 添加新规则
- 点击右上角的"+"按钮
- 填写规则信息：
  - 规则名称（必填）
  - 规则类型（必选）
  - 规则描述（可选）
  - 触发条件（根据规则类型显示）
  - 赠送课程（必选）
  - 赠送课时数（必填）
  - 有效期设置（可选）
  - 限制条件（可选）
- 点击"创建"按钮保存

### 3. 编辑规则
- 点击规则卡片上的"编辑"按钮
- 修改需要更改的信息
- 点击"更新"按钮保存

### 4. 管理规则状态
- 点击规则卡片上的"启用"/"禁用"按钮
- 确认操作后状态会立即更新

### 5. 删除规则
- 点击规则卡片上的"删除"按钮
- 确认删除操作
- 规则将被永久删除

## 注意事项

1. 删除规则前请确认该规则没有关联的赠送记录
2. 修改规则状态会影响新的赠送记录生成
3. 规则的有效期设置会影响规则的自动启用和禁用
4. 每人最大享受次数和每月最大次数用于限制规则的使用频率

## 技术实现

- 基于 uni-app 框架开发
- 使用 uView UI 组件库
- 支持微信小程序平台
- 响应式设计，适配不同屏幕尺寸
- 使用 async/await 处理异步操作
- 完整的错误处理和用户提示

## 测试

可以使用 `test.vue` 页面进行基本的API接口测试，确保后端接口正常工作。
