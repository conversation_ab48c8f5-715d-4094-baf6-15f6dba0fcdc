# API参数过滤示例

## 问题描述

在调用后端API时，如果传递了null、undefined或空字符串参数，可能会导致：
1. 后端查询异常
2. 不必要的网络传输
3. 查询性能下降

## 解决方案

通过 `filterParams` 方法自动过滤无效参数：

```javascript
// 过滤参数，移除null、undefined和空字符串
filterParams(params) {
    const filteredParams = {}
    Object.keys(params).forEach(key => {
        const value = params[key]
        if (value !== null && value !== undefined && value !== '') {
            filteredParams[key] = value
        }
    })
    return filteredParams
}
```

## 使用示例

### 原始参数
```javascript
const queryParams = {
    pageNum: 1,
    pageSize: 10,
    ruleName: null,      // 无效参数
    ruleType: '',        // 无效参数
    status: undefined    // 无效参数
}
```

### 过滤后的参数
```javascript
const filteredParams = this.filterParams(queryParams)
// 结果：
{
    pageNum: 1,
    pageSize: 10
    // null、空字符串和undefined参数被过滤掉了
}
```

### API调用
```javascript
// 使用过滤后的参数调用API
const response = await getGiftRuleList(filteredParams)
```

## 实际效果

### 优化前的网络请求
```
Query String Parameters:
pageNum: 1
pageSize: 10
ruleName: null
ruleType: null
status: null
```

### 优化后的网络请求
```
Query String Parameters:
pageNum: 1
pageSize: 10
```

## 应用场景

1. **列表查询**: 过滤搜索和筛选条件中的空值
2. **表单提交**: 过滤表单中的空字段
3. **分页请求**: 只传递必要的分页参数

## 优势

1. **减少网络传输**: 不传递无用的参数
2. **提高性能**: 减少后端处理负担
3. **避免错误**: 防止null值导致的查询异常
4. **代码简洁**: 统一的参数处理逻辑

## 测试验证

可以在测试页面中验证参数过滤效果：

```javascript
// 测试参数过滤
const testParams = {
    pageNum: 1,
    pageSize: 10,
    ruleName: null,
    ruleType: '',
    status: undefined
}

const filteredParams = this.filterParams(testParams)
console.log('过滤前:', testParams)
console.log('过滤后:', filteredParams)
```

## 注意事项

1. **数字0**: 数字0是有效值，不会被过滤
2. **布尔false**: 布尔值false是有效值，不会被过滤
3. **空数组**: 空数组[]是有效值，不会被过滤
4. **空对象**: 空对象{}是有效值，不会被过滤

## 扩展应用

这个参数过滤方法可以应用到项目的其他页面和API调用中，建议：

1. 提取为全局工具方法
2. 在request拦截器中统一处理
3. 添加到项目的工具库中
